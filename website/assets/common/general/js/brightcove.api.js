var video_id = $(".embed-responsive").data('id');
var first_request = true;

if (document.getElementsByTagName('video')) {
  var myPlayer = videojs($('video').attr('id'));
} else {
  var myPlayer = videojs('myPlayerID_html5_api');
}
$.ajaxSetup({
  beforeSend: function (jqXHR, Obj) {
    var value = "; " + document.cookie;
    var parts = value.split("; csrf_vs_cookie=");
    if (parts.length == 2)
      Obj.data += '&csrf_vs_token=' + parts.pop().split(";").shift();
  }
});
myPlayer.on('loadedmetadata', function () {
  $.ajax({
    url: '/ajax/post/get_current_time',
    method: 'POST',
    async: false,
    cache: false,
    data: {
      video_id: video_id,
    },
    success: function (result) {
      result = $.parseJSON(result);
      myPlayer.currentTime(result.current_time);
    },
    error: function (e) {
      //TODO: hata mesajını düzenle
      //alert('An error occured.');
      //window.location.reload();
    },
  });
});
myPlayer.on('play', function () {
  setInterval(function () {
    var current_time = myPlayer.currentTime();
    var duration = myPlayer.duration();

    $.ajax({
      url: '/ajax/post/update_current_time',
      method: 'POST',
      async: false,
      cache: false,
      data: {
        video_id: video_id,
        current_time: current_time,
        duration: duration,
        first_request: first_request,
      },
      success: function (result) {
        //window.location.reload();
        if (first_request) {
          first_request = false;
        }
      },
      error: function (e) {
        //TODO: hata mesajını düzenle
        //alert('An error occured.');
        //window.location.reload();
      },
    });
    if ('function' === typeof get_current_time) {
      get_current_time(current_time, duration);
    }
  }, 10000);
});