<?php

class Vi_interactive_tools extends vipanelController
{
    public function index()
    {
        $this->load->helper('interactive_tools');
        $survey_block_check = blockCheck('survey');
        return $this->template('interactive_tools/index', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Interactive Tools'
            ],
            'post_survey_setup' => [
                'survey_block_check' => $survey_block_check
            ]
        ]);
    }

    public function save()
    {
        $this->isPost();

        if (!empty($_FILES)) {
            $type = $this->input->post('type', true);
            $allData = $this->input->post(null, true);
        } else {
            $type = atm_base64_decode($this->input->post('type', true));
            $allData = $this->parsePostData($this->input->post(null, true));
        }

        $this->load->helper('interactive_tools');

        switch ($type) {
            case "createPresenterUser":
                $status = createPresenterUser($allData);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'description' => 'Moderator user created: "' . $allData['username'] . '"',
                        ],
                    );
                }
                break;
            case "createModeratorUser":
                $status = createModeratorUser($allData);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'description' => 'Moderator user created: "' . $allData['username'] . '"',
                        ],
                    );
                }
                break;
            case "userList":
                $status = listUsers($allData);
                break;
            case "deleteUser":
                $user_id = (int)$allData['itemId'];
                $userInfo = getUserInfo($user_id);
                if (isset($userInfo) && !empty($userInfo)) {
                    $userType = ($userInfo->type == "20") ? 'Presenter' : (($userInfo->type == "30") ? 'Moderator' : 'unknown');
                    $username = $userInfo->username;
                }
                $status = deleteUsers($allData);
                if ($status) {
                    $this->log_user_action(
                        'DELETE_USER',
                        [
                            'description' => ' ' . $userType . ' User deleted: "' . $username . '"',
                        ],
                    );
                }
                break;
            case "update_post_survey_blocks":
                $status = updateCustomBlock('survey_header', htmlspecialchars_decode($allData['header']));
                if ($status) {
                    $status = updateCustomBlock('survey_footer', htmlspecialchars_decode($allData['footer']));
                }
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'description' => 'Survey header/footer blocks updated!'
                        ]
                    );
                }
                break;
            case "create_post_survey_question":
                $status = createSurveyQuestion($allData, true);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        ['section' => 'Create Post Survey Question',
                            'new_value' => $allData['question'],
                            'description' => 'Post survey question created: "' . $allData['question'] . '"'
                        ]
                    );
                }
                break;
            case "edit_post_survey_question":
                $new_options = [];
                $section = "";
                foreach ($allData["options"] as $key => $value) {
                    if (strpos($key, "new") === 0) {
                        $new_options[] = $value;
                        break;
                    }
                }
                if (!empty($new_options)) {
                    $section = 'Create a new option: "' . implode(',', $new_options) . '" ';
                }
                $status = editSurveyQuestion($allData, true);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $section,
                            'description' => 'Post survey question edited: "' . $allData['question'] . '"'
                        ]
                    );
                }
                break;
            case "delete_survey_question":
                $question_id = (int)$allData['id'];
                $question = getSurveyQuestion($question_id);
                $status = deleteSurveyQuestion($allData['id']);
                if ($status) {
                    $this->log_user_action(
                        'DELETE_SURVEY_QUESTION',
                        ['old_value' => $question->question,
                            'description' => 'Post survey question deleted: "' . $question->question . '"'
                        ]
                    );
                }
                break;
            case "create_pre_survey_question":
                $status = createSurveyQuestion($allData, false);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        ['section' => 'Create Pre Survey Question',
                            'new_value' => $allData['question'],
                            'description' => 'Pre survey question created: "' . $allData['question'] . '"'
                        ]
                    );
                }
                break;
            case "edit_pre_survey_question":
                $new_options = [];
                $section = "";
                foreach ($allData["options"] as $key => $value) {
                    if (strpos($key, "new") === 0) {
                        $new_options[] = $value;
                        break;
                    }
                }
                if (!empty($new_options)) {
                    $section = 'Create a new option: "' . implode(',', $new_options) . '" ';
                }
                $status = editSurveyQuestion($allData, false);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $section,
                            'description' => 'Pre survey question edited: "' . $allData['question'] . '"'
                        ]
                    );
                }
                break;
            case "create_polling_question":
                $status = createPollingQuestion($allData);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'description' => 'Polling question created: "' . $allData['question'] . '"',
                        ],
                    );
                }
                break;
            case "edit_polling_question":
                $new_options = [];
                $section = "";

                if (isset($allData['answer'][0]) && is_array($allData['answer'][0])) {
                    $section = "New Answer(s) added : ";
                    $new_options = array_merge($new_options, $allData['answer'][0]);

                    foreach ($new_options as $new_option) {
                        $section .= $new_option . ',';
                    }
                }

                $status = editPollingQuestion($allData);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $section,
                            'description' => 'Polling question edited: "' . $allData['question'] . '"'
                        ]
                    );
                }
                break;
            case "delete_polling_question":
                $question_id = (int)$allData['id'];
                $question = getPollingQuestion_log($question_id);
                $status = deletePollingQuestion($allData['id']);
                if ($status) {
                    $this->log_user_action(
                        'DELETE_POLLING_QUESTION',
                        [
                            'description' => 'Polling question deleted: "' . $question->question . '"'
                        ]
                    );
                }
                break;
            case "delete_polling_votes":
                $question_id = (int)$allData['id'];
                $question = getPollingQuestion_log($question_id);
                $status = deletePollingVotes($allData['id']);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $allData['type'],
                            'description' => 'Polling Question Votes deleted: "' . $question->question . '"'
                        ]
                    );
                }
                break;
            case "uploadCertificate":
                $result = uploadCertificate($allData);
                if ($result) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'description' => 'Certifiace Uploaded'
                        ]
                    );
                }
                return $this->json($result);
                break;
            case "send_notification":
                $status = sendNotification($allData);
                if ($status) {
                    $description = "Title: {$allData['title']} - Related Link: {$allData['related-link']} - Description: {$allData['description']}";
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $allData['type'],
                            'description' => $description
                        ]
                    );
                }
                break;
            case "delete_notification":
                $notification_id = (int)$allData['id'];
                $notification = getNotification_log($notification_id);
                $status = deleteNotification($allData['id']);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $allData['type'],
                            'description' => 'Notification deleted: "' . $notification->title . '"'
                        ]
                    );
                }
                break;
            case "create_announcement":
                $status = createAnnouncement($allData);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $allData['type'],
                            'description' => 'Announcement created: "' . $allData['content'] . '"'
                        ]
                    );
                }
                break;
            case "delete_announcement":
                $status = deleteAnnouncement($allData['id']);
                if ($status) {
                    $this->log_user_action(
                        'INTERACTIVE_TOOLS',
                        [
                            'section' => $allData['type'],
                            'description' => 'Announcement deleted: "' . $allData['id'] . '"'
                        ]
                    );
                }
                break;
            case "send_topic":
                $status = sendTopic($allData);
                break;
            case "delete_topic":
                $status = deleteTopic($allData['id']);
                break;
            case "delete_comment":
                $status = deleteComment($allData['id']);
                break;
            case "edit_topic":
                $status = editTopic($allData);
                break;
            case "create_quiz_question":
                $quiz = new QuizQuestions();
                $status = $quiz->createQuestion($allData);
                break;
            case "edit_quiz_question":
                $quiz = new QuizQuestions();
                $status = $quiz->editQuestion($allData);
                break;
            case "delete_quiz_question":
                $quiz = new QuizQuestions();
                $status = $quiz->deleteQuestion($allData);
                break;
            case "delete_quiz_question_option":
                $quiz = new QuizQuestions();
                $status = $quiz->deleteOption($allData['option_id'], $allData['correct_id']);
                break;
            default:
                $status = false;
                break;
        }

        return $this->json([
            'status' => $status
        ]);
    }

    public function certificateTest()
    {
        $text_color = project_config('certificate_text_color');
        $text_color = strpos($text_color, ',') !== false ? $text_color : '9, 78, 111';

        $user = new stdClass();
        $user->fullname = 'Test User';
        $this->session->set_userdata('site_user', $user);

        echo generateCertificate(
            '2018-01-01 00:00:00',
            '2036-01-01 00:00:00',
            false,
            project_config('certificate_image'),
            project_config('certificate_redirect_to') ?: 'stream',
            $text_color ? explode(',', $text_color) : [9, 78, 111],
            project_config('certificate_text_x') ?: 1,
            project_config('certificate_text_y') ?: 68,
            0,
            true,
            project_config('certificate_font') ?: null,
            project_config('certificate_font_size'),
            project_config('certificate_frame') ?: 'L',
            1,
            'I',
            false
        );
    }

    public function certificateFontPreview()
    {
        ini_set('display_errors', 1);
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_USER_NOTICE & ~E_USER_DEPRECATED & ~E_WARNING);

        require APPPATH . 'third_party/tcpdf/tcpdf.php';

        $fonts = ["parisienne", "aealarabiya", "aefurat", "cid0cs", "cid0ct", "cid0jp", "cid0kr", "courier", "courierb", "courierbi", "courieri", "dejavusans", "dejavusansb", "dejavusansbi", "dejavusanscondensed", "dejavusanscondensedb", "dejavusanscondensedbi", "dejavusanscondensedi", "dejavusansextralight", "dejavusansi", "dejavusansmono", "dejavusansmonob", "dejavusansmonobi", "dejavusansmonoi", "dejavuserif", "dejavuserifb", "dejavuserifbi", "dejavuserifcondensed", "dejavuserifcondensedb", "dejavuserifcondensedbi", "dejavuserifcondensedi", "dejavuserifi", "freemono", "freemonob", "freemonobi", "freemonoi", "freesans", "freesansb", "freesansbi", "freesansi", "freeserif", "freeserifb", "freeserifbi", "freeserifi", "helvetica", "helveticab", "helveticabi", "helveticai", "hysmyeongjostdmedium", "kozgopromedium", "kozminproregular", "msungstdlight", "pdfacourier", "pdfacourierb", "pdfacourierbi", "pdfacourieri", "pdfahelvetica", "pdfahelveticab", "pdfahelveticabi", "pdfahelveticai", "pdfasymbol", "pdfatimes", "pdfatimesb", "pdfatimesbi", "pdfatimesi", "stsongstdlight", "symbol", "times", "timesb", "timesbi", "timesi"];

        $pageSize = array(413, 584);
        $pdf = new TCPDF('P', "px", $pageSize, true, 'UTF-8', false);

        $pdf->SetCreator(PDF_CREATOR);
        $pdf->SetAuthor('Author');
        $pdf->SetTitle('Font Test');

        $pdf->SetMargins(15, 15, 15);
        $pdf->SetAutoPageBreak(true, 15);

        $pdf->SetFont('helvetica', '', 12);

        $pdf->AddPage();

        foreach ($fonts as $font) {
            $pdf->SetFont($font, '', 15);
            $pdf->Cell(0, 10, "Preview Font: $font", 0, 1);
        }

        $pdf->Output('font_test.pdf', 'I');
    }

    public function surveyTest()
    {
        $this->load->model('M_interactive_tools', 'm_interactive_tools');

        $this->data['setLayout'] = 'new_general';
        $type = ($this->input->get('type', true) ?? 'true') == 'true';
        $survey_no = $this->input->get('no', true) ?? 1;
        $this->data['survey_type'] = $type;
        $this->data['survey_no'] = $survey_no;

        ##curent language
        $current_lang =
            $this->session->userdata('site_config')->current_lang ?:
                $this->session->userdata('site_config')->default_lang;

        ##include language keys from file if exists
        $lang_file = APPPATH . 'language/lang_' . $current_lang . '.php';

        $this->data['all_language_keys'] = '{}';
        if (file_exists($lang_file)) {
            include $lang_file;

            // TODO: aşağıdaki $custom_lang_keys bunları ezmeli!!
            $this->data['all_language_keys'] = '{' . str_replace(
                    ['<?php', '$this->data[', ']=', '\';', PHP_EOL],
                    ['', '', ':', '\',', ''],
                    file_get_contents($lang_file)
                ) . '}';
        }

        ##get site based language keys from db
        //$general_lang_keys = $this->m_lang->get_general_keys($project_id, $this->input->cookie('site_lang', true));
        $general_lang_keys = $this->m_interactive_tools->get_general_keys($current_lang);
        foreach ($general_lang_keys as $lang) {
            $this->data[$lang->key] = $lang->value;
        }
        //$custom_lang_keys = $this->m_lang->get_custom_keys($project_id, $this->input->cookie('site_lang', true));
        $custom_lang_keys = $this->m_interactive_tools->get_custom_keys(site_config('project_id'), $current_lang);
        foreach ($custom_lang_keys as $lang) {
            $this->data[$lang->key] = $lang->value;
        }

        $survey = $this->m_interactive_tools->getSurveyPageContent(site_config('project_id'));
        if (!empty($survey)) {
            if ($survey->template_id == null) {
                $this->data['survey_content'] = $survey->content ?? null;
            }
        }

        $this->parser->parse('others/survey_test', $this->data);
    }

    public function pollingResult()
    {
        $this->load->model('M_interactive_tools', 'm_interactive_tools');

        $question_id = $this->input->get('question', true);
        $this->data['calculatePercent'] = $this->input->get('type', true) == 1;

        $results = $this->m_interactive_tools->getPollingForResultPage($question_id);

        $this->data['question'] = $this->m_interactive_tools->getPollingQuestion(site_config('project_id'), $question_id, site_config('stream_lang'));

        $this->data['countries'] = [];
        $this->data['results'] = [];
        $this->data['total_vote_count'] = 0;
        $this->data['vote_count_by_question'] = 0;
        foreach ($results as $result) {
            $countryBasedVotes = [];
            if ($result->vote_count) {
                $votedCountries = explode(',', $result->countries);
                $this->data['countries'][] = $votedCountries;

                $countryBasedVotes = array_count_values($votedCountries);
            }

            $this->data['results'][$result->id] = [
                'answer' => $result->answer,
                'vote_count' => (int)$result->vote_count,
                'countries' => $countryBasedVotes,
            ];

            $this->data['total_vote_count'] += (int)$result->vote_count;
        }

        $this->data['countries'] = array_unique(
            array_flatten(
                $this->data['countries']
            )
        );

        sort($this->data['countries']);

        $answersWithCountries = $this->m_interactive_tools->getPollingAnswersWithCountries($question_id);

        $countsByCountries = [];
        foreach ($answersWithCountries as $answersWithCountry) {
            if (isset($countsByCountries["{$answersWithCountry->country}"])) {
                $countsByCountries["{$answersWithCountry->country}"] += 1;
            }
        }

        $this->data['countsByCountries'] = $countsByCountries;

        $this->load->view('polling/result_table', $this->data);
    }

    public function getComments()
    {
        $this->load->model('M_interactive_tools', 'm_interactive_tools');
        $encoded_id = $this->input->post('id');
        $decoded_id = base64_decode($encoded_id);
        $order_by = $this->input->post('order_by') ?: 'desc';

        if (!$decoded_id) {
            echo json_encode(['error' => 'No valid ID provided']);
            return;
        }

        $comments = $this->m_interactive_tools->get_topic($decoded_id, $order_by);

        echo json_encode($comments);
    }

    public function quizReports()
    {
        $this->load->helper('admin_settings');

        $data = getQuizReport();
        $columns = [
            'id' => 'Q ID',
            'project_id' => 'Project ID',
            'fullname' => 'Fullname',
            'quiz_no' => 'Quiz No',
            'embeds' => 'Embeds',
            'question' => 'Question',
            'order' => 'Order',
            'type' => 'Type',
            'option' => 'Answer',
            'created_at' => 'Answered Date',
            'result' => 'Result'
        ];

        echo json_encode([
            'status' => true,
            'data' => $data,
            'columns' => $columns
        ]);
    }

}
