<?php

class Vi_datalake extends vipanelController
{
    public $viportal_address = ENV_VIPORTAL_URL . '/stream-detail?stream_id=';
    public $datalake_address = ENV_DATALAKE_URL . 'api/v1/sync';

    public function sendData()
    {
        set_time_limit(0);
        $sendgrid = $this->syncSendgrid();

        if (!$sendgrid['status']) {
            return $this->json([
                'status' => 'error',
                'message' => $sendgrid['message'],
                'data' => []
            ]);
        }

        $status = 'success';
        $message = '';

        $selectedStreamCode = $this->input->get('stream_code');
        $projectDetails = $this->session->userdata('site_config');
        $viportal_code = json_decode($projectDetails->stream_code, true);

        $getPortalData = [];
        if ($selectedStreamCode && $selectedStreamCode != 'all') {
            $getPortalData[] = $this->getPortalData($selectedStreamCode);
        } else {
            foreach ($viportal_code as $code) {
                $selectedStreamCode = "";
                $getPortalData[] = $this->getPortalData($code);
            }
        }

        $getRegisterData = $this->getRegisterReport($selectedStreamCode);

        if ($getPortalData) {
            $stylizeData = $this->stylizeAndFillData($projectDetails->url, $getPortalData, $getRegisterData);
            $sendDataToDatalake = $this->sendDataToDatalake($stylizeData);

            if ($sendDataToDatalake) {
                $message = $sendDataToDatalake['data']['message'] ?? '';
            }
        } else {
            $status = 'error';
            $message = 'Portal data not found';
        }
        if (isset($sendDataToDatalake['exception'])) {
            $status = 'error';
            $message = $sendDataToDatalake['message'] ?? '';
        }
        if (isset($sendDataToDatalake['errors'])) {
            $status = 'error';
            $message = 'Errors occurred: ' . json_encode($sendDataToDatalake['message']);
        }

        return $this->json([
            'status' => $status,
            'message' => $message,
            'data' => $sendDataToDatalake ?? []
        ]);
    }

    public function sendDataToDatalake($data)
    {
        $ch = curl_init($this->datalake_address);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_USERPWD, ENV_DATALAKE_AUTH_USER . ':' . ENV_DATALAKE_AUTH_PASS);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            curl_close($ch);
            return 'cURL error: ' . $error_msg;
        }

        curl_close($ch);

        return json_decode($response, true);
    }

    function getRegisterReport($form_id = null)
    {
        $CI = &get_instance();
        $CI->load->model('M_admin_settings', 'm_admin_settings');

        if ($form_id) {
            $data = $CI->m_admin_settings->getRegisterReportWithFormId(site_config('project_id'), $form_id);
        } else {
            $data = $CI->m_admin_settings->getRegisterReport(site_config('project_id'));
        }

        return array_map(function ($item) {
            $data = [];
            $item->data = strip_tags($item->data);
            foreach (json_decode($item->data) as $key => $value) {
                $data[$key] = is_array($value) ? implode(', ', $value) : $value;
            }
            $data['create_date'] = $item->create_date;

            return $data;
        }, $data);
    }

    public function getPortalData($viportal_code)
    {
        $viportal_address = $this->viportal_address . $viportal_code;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $viportal_address);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            curl_close($ch);
            return 'cURL error: ' . $error_msg;
        }

        curl_close($ch);

        return json_decode($response);
    }

    public function stylizeAndFillData($streamCode, $portalDataV, $registerData)
    {
        $portalData = [];
        foreach ($portalDataV as $data) {
            if (
                is_object($data)
                && property_exists($data, 'data')
                && property_exists($data->data, 'events_id')
            ) {
                $portalData[] = $data->data;
            }
        }

        $newData = [
            'events' => [
                'code' => $streamCode,
                'company' => $portalData[0]->event[0]->customer_name ?? null,
                'region' => $portalData[0]->event[0]->region ?? null,
                'series_name' => $portalData[0]->event[0]->series_name ?? null,
                'name' => $portalData[0]->event[0]->name ?? null,
                'product' => $portalData[0]->event[0]->product_name ?? null,
                'business_owner_name' => $portalData[0]->business_owner_name ?? null,
                'business_owner_email' => $portalData[0]->business_owner_email ?? null,
                'webinars' => []
            ]
        ];

        foreach ($portalData as $data) {
            $webinar = [
                'webinar_name' => $data->title,
                'webinar_code' => $data->stream_id,
                'service_type' => $data->servis_turu,
                'webinar_type' => $data->session_type,
                'start_date' => $data->start_date,
                'end_date' => $data->end_date,
                'location' => "",
                'host_type' => $data->host_type,
                'host_country' => $data->host_country,
                'webinar_cluster' => $data->webinar_cluster,
                'website_url' => $data->url,
                'language' => $data->languages_id,
                'breakout_check' => $data->breakout_check ?? false,
                'survey' => $data->survey_mevcut ?? false,
                'polling' => $data->polling_mevcut ?? false,
                'certificate' => $data->certificate_mevcut ?? false,
                'register_check' => 1,
                'registers' => []
            ];

            // Extract all email addresses first
            $emails = [];
            foreach ($registerData as $register) {
                $register = json_decode(json_encode($register));
                $emails[] = $register->email;
            }

            // Fetch all data in batch operations
            $vipanelAccessStatuses = $this->getBatchVipanelAccessStatus($emails);
            $interactiveControls = $this->getBatchRegisterInteractiveControl($emails);
            $embedReports = $this->getBatchEmbedReport($emails);

            foreach ($registerData as $register) {
                $register = json_decode(json_encode($register));
                $vipanelAccessStatus = $vipanelAccessStatuses[$register->email] ?? 'pending';
                $interactiveControl = $interactiveControls[$register->email] ?? ['survey' => false, 'polling' => false, 'certificate' => false];
                $embedReport = $embedReports[$register->email] ?? [];

                $registerEntry = [
                    'first_name' => $register->first_name ?? '',
                    'last_name' => $register->last_name ?? '',
                    'full_name' => !empty($register->fullname) ? $register->fullname : ($register->first_name ?? '') . ' ' . ($register->last_name ?? ''),
                    'email' => $register->email ?? '',
                    'country' => $register->country ?? '',
                    'speciality' => $register->speciality ?? '',
                    'institution' => $register->institution ?? '',
                    'hcp_number' => $register->hcp_number ?? '',
                    'hcp_phone_number' => $register->hcp_phone_number ?? '',
                    'campaign_tracking' => [
                        'source' => $register->utm_source ?? '',
                        'medium' => $register->utm_medium ?? '',
                        'campaign' => $register->utm_campaign ?? '',
                    ],
                    'extra' => [
                        'notes' => $register->notes ?? '',
                    ],
                    'form_name' => $register->form_name ?? '',
                    'register_checks' => [
                        'unsubscribe' => false,
                        'confirmation' => [
                            'status' => $vipanelAccessStatus == 'confirmed' ? true : false,
                            'confirmation_primary_cta' => false,
                        ],
                        'pending' => [
                            'status' => $vipanelAccessStatus == 'pending' ? true : false,
                            'pending_primary_cta' => false,
                        ],
                        'rejection' => [
                            'status' => $vipanelAccessStatus == 'rejected' ? true : false,
                            'rejection_primary_cta' => false,
                        ],
                        'reminder' => [],
                        'thank_you_email' => [
                            'status' => false,
                            'email_primary_cta' => false,
                        ],
                        'miss_you_email' => [
                            'status' => false,
                            'email_primary_cta' => false,
                        ],
                        'survey_submission' => $interactiveControl['survey'] ?? false,
                        'polling' => $interactiveControl['polling'] ?? false,
                        'q_a' => false,
                        'certificate' => $interactiveControl['certificate'] ?? false,
                    ],
                    'register_stats' => [
                        'total_webinar_duration' => (int) project_config('webinar_length') ?: 0,
                        'total_live_watching_time' => 0,
                        'embed_duration' => []
                    ]
                ];

                $totalWatching = 0;
                foreach ($embedReport as $embed) {
                    $elapsed_time_minutes = round($embed->elapsed_time / 60, 2);
                    $totalWatching += $elapsed_time_minutes;
                    $registerEntry['register_stats']['embed_duration'][] = [
                        'embed' => $embed->embed,
                        'duration' => (int) $elapsed_time_minutes
                    ];
                }
                $registerEntry['register_stats']['total_live_watching_time'] = (int) $totalWatching;

                $webinar['registers'][] = $registerEntry;
            }

            $newData['events']['webinars'][] = $webinar;
        }

        return $newData;
    }

    /*Helper Functions*/
    public function getVipanelAccessStatus($email)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        if (project_config('vipanel_access') === "1") {
            $project_id = site_config('project_id');
            $query = "SELECT status FROM vipanel_approval
                WHERE project_id = '{$project_id}' AND email = '{$email}'";
            $q = $this->executeAndLogQuery($CI, $query);

            if ($q && $q->num_rows() > 0) {
                return $q->row()->status == 0 ? 'rejected' : 'confirmed';
            }
            return 'pending';
        }
        return false;
    }

    /**
     * Get all data for a register in a single database call
     * This method combines the functionality of getVipanelAccessStatus, registerInteractiveControl, and getEmbedReport
     * to avoid the n+1 problem
     */
    public function getRegisterData($email)
    {
        // Use the batch methods with a single email to leverage their optimizations
        $emails = [$email];

        $vipanelAccessStatuses = $this->getBatchVipanelAccessStatus($emails);
        $interactiveControls = $this->getBatchRegisterInteractiveControl($emails);
        $embedReports = $this->getBatchEmbedReport($emails);

        return [
            'vipanelAccessStatus' => $vipanelAccessStatuses[$email] ?? 'pending',
            'interactiveControl' => $interactiveControls[$email] ?? [
                'survey' => false,
                'polling' => false,
                'certificate' => false
            ],
            'embedReport' => $embedReports[$email] ?? []
        ];
    }

    public function getBatchVipanelAccessStatus($emails)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        // Try to load cache driver
        $useCache = false;
        try {
            $CI->load->driver('cache', ['adapter' => 'memcached']);
            $useCache = true;
        } catch (Exception $e) {
            // If cache loading fails, continue without caching
        }

        $result = [];
        $uncachedEmails = [];

        // Initialize all emails with 'pending' status and check cache
        foreach ($emails as $email) {
            $result[$email] = 'pending';

            // Try to get from cache if available
            if ($useCache) {
                $cacheKey = 'vipanel_status_' . md5($email . '_' . site_config('project_id'));
                $cachedStatus = $CI->cache->get($cacheKey);

                if ($cachedStatus !== false) {
                    $result[$email] = $cachedStatus;
                } else {
                    $uncachedEmails[] = $email;
                }
            } else {
                $uncachedEmails[] = $email;
            }
        }

        if (project_config('vipanel_access') === "1" && !empty($uncachedEmails)) {
            // Limit the number of emails per query to avoid too large queries
            $emailChunks = array_chunk($uncachedEmails, 100);
            $project_id = site_config('project_id');

            foreach ($emailChunks as $chunk) {
                $emailList = "'" . implode("','", $chunk) . "'";

                $query = "SELECT email, status FROM vipanel_approval
                    WHERE project_id = '{$project_id}' AND email IN ({$emailList})";
                $queryResult = $this->executeAndLogQuery($CI, $query);

                if ($queryResult && $queryResult->num_rows() > 0) {
                    foreach ($queryResult->result() as $row) {
                        $status = $row->status == 0 ? 'rejected' : 'confirmed';
                        $result[$row->email] = $status;

                        // Store in cache if available
                        if ($useCache) {
                            $cacheKey = 'vipanel_status_' . md5($row->email . '_' . $project_id);
                            $CI->cache->save($cacheKey, $status, 3600); // Cache for 1 hour
                        }
                    }
                }
            }
        }

        return $result;
    }

    public function registerInteractiveControl($email)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        $data = [];
        $project_id = site_config('project_id');

        $checks = ['survey' => 'survey', 'polling' => 'polling_logs', 'certificate' => 'certificates'];
        foreach ($checks as $key => $table) {
            $query = "SELECT 1 FROM {$table}
                INNER JOIN log ON log.id = {$table}.log_id
                WHERE project_id = '{$project_id}'
                AND log.email LIKE '%{$email}%'
                LIMIT 1";
            $queryResult = $this->executeAndLogQuery($CI, $query);

            $data[$key] = ($queryResult && $queryResult->num_rows() > 0) ? true : false;
        }

        return $data;
    }

    public function getBatchRegisterInteractiveControl($emails)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        $result = [];
        $project_id = site_config('project_id');

        // Initialize all emails with default values
        foreach ($emails as $email) {
            $result[$email] = [
                'survey' => false,
                'polling' => false,
                'certificate' => false
            ];
        }

        if (!empty($emails)) {
            // Limit the number of emails per query to avoid too large queries
            $emailChunks = array_chunk($emails, 100);
            $checks = ['survey' => 'survey', 'polling' => 'polling_logs', 'certificate' => 'certificates'];

            foreach ($emailChunks as $chunk) {
                $emailList = "'" . implode("','", $chunk) . "'";

                // Build a UNION query to get all data in one go
                $unionQueries = [];

                foreach ($checks as $key => $table) {
                    $unionQueries[] = "SELECT '{$key}' as control_type, log.email
                        FROM {$table}
                        INNER JOIN log ON log.id = {$table}.log_id
                        WHERE project_id = '{$project_id}'
                        AND log.email IN ({$emailList})";
                }

                $query = implode(" UNION ALL ", $unionQueries);
                $queryResult = $this->executeAndLogQuery($CI, $query);

                if ($queryResult && $queryResult->num_rows() > 0) {
                    foreach ($queryResult->result() as $row) {
                        if (isset($result[$row->email]) && isset($row->control_type)) {
                            $result[$row->email][$row->control_type] = true;
                        }
                    }
                }
            }
        }

        return $result;
    }

    public function getEmbedReport($email)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        $query = "select embed_log.embed, log.email, SUM(TIMESTAMPDIFF(SECOND,created_at,updated_at)) as elapsed_time
                from embed_log
                    inner join log on log.id=embed_log.log_id
                where event != 0 and embed_log.project_id = '" . site_config('project_id') . "' and log.email = '" . $email . "'
                group by embed,email";
        $q = $this->executeAndLogQuery($CI, $query);

        return $q->result();
    }

    public function getBatchEmbedReport($emails)
    {
        // Get CI instance to access database
        $CI = &get_instance();
        $CI->load->database();

        $result = [];

        // Initialize all emails with empty arrays
        foreach ($emails as $email) {
            $result[$email] = [];
        }

        if (!empty($emails)) {
            // Limit the number of emails per query to avoid too large queries
            $emailChunks = array_chunk($emails, 100);
            $project_id = site_config('project_id');

            foreach ($emailChunks as $chunk) {
                // Convert array to comma-separated string with quotes
                $emailList = "'" . implode("','", $chunk) . "'";

                // Use FORCE INDEX hint if the query is slow due to poor index selection
                $query = "select embed_log.embed, log.email, SUM(TIMESTAMPDIFF(SECOND,created_at,updated_at)) as elapsed_time
                        from embed_log
                            inner join log on log.id=embed_log.log_id
                        where event != 0
                        and embed_log.project_id = '{$project_id}'
                        and log.email IN ({$emailList})
                        group by embed,email";
                $q = $this->executeAndLogQuery($CI, $query);

                if ($q && $q->num_rows() > 0) {
                    foreach ($q->result() as $row) {
                        $result[$row->email][] = $row;
                    }
                }
            }
        }

        return $result;
    }

    public function syncSendgrid()
    {
        try {
            $this->load->helper('sendgrid_helper');
            $sg = new SendGridService();
            $lists = $sg->getLists(true);
            foreach ($lists as $list) {
                if ($list->project_id == site_config('project_id')) {
                    $sg->getSingleSendStats($list->sendgrid_id, true);
                    $sg->getSingleSendLinkStats($list->sendgrid_id, true);
                }
            }
            return [
                'status' => true,
                'message' => 'Sendgrid synced successfully'
            ];
        } catch (Exception $e) {
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function json($data, $status = 200)
    {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        return true;
    }

    /**
     * Log slow queries for performance monitoring
     * @param string $query The SQL query to execute
     * @param float $threshold Time threshold in seconds to consider a query slow (default: 1.0)
     * @return mixed Query result
     */
    protected function executeAndLogQuery($CI, $query, $threshold = 1.0)
    {
        // Start timing
        $start_time = microtime(true);

        // Execute query
        $result = $CI->db->query($query);

        // Calculate execution time
        $execution_time = microtime(true) - $start_time;

        // Log slow queries
        if ($execution_time > $threshold) {
            $log_message = sprintf(
                "SLOW QUERY (%.4f sec): %s",
                $execution_time,
                $query
            );

            // Log to file
            $CI->load->helper('file');
            $log_file = APPPATH . 'logs/slow_queries_' . date('Y-m-d') . '.log';
            write_file($log_file, date('Y-m-d H:i:s') . ' - ' . $log_message . PHP_EOL, 'a+');

            // Analyze query and suggest indexes
            $this->suggestIndexes($CI, $query, $execution_time, $log_file);
        }

        return $result;
    }

    /**
     * Analyze a slow query and suggest indexes that might improve performance
     * @param object $CI CodeIgniter instance
     * @param string $query The SQL query to analyze
     * @param float $execution_time Query execution time
     * @param string $log_file Path to log file
     */
    protected function suggestIndexes($CI, $query, $execution_time, $log_file)
    {
        // Extract table names from the query
        preg_match_all('/\bFROM\s+`?(\w+)`?|\bJOIN\s+`?(\w+)`?/i', $query, $matches);

        $tables = array_filter(array_merge($matches[1], $matches[2]));

        // Extract WHERE conditions
        preg_match_all('/\bWHERE\s+(.*?)(?:\bGROUP BY|\bORDER BY|\bLIMIT|\bHAVING|\bUNION|\bINTO|\bFOR UPDATE|\bLOCK IN|\)$|$)/is', $query, $where_matches);

        $where_conditions = [];
        if (!empty($where_matches[1])) {
            $where_clause = $where_matches[1][0];
            preg_match_all('/(\w+\.\w+|\w+)\s*(?:=|LIKE|IN|>|<|>=|<=|<>|!=)/i', $where_clause, $condition_matches);
            $where_conditions = $condition_matches[1];
        }

        // Extract JOIN conditions
        preg_match_all('/\bJOIN\s+`?(\w+)`?\s+.*?\bON\s+(.*?)(?:\bWHERE|\bGROUP BY|\bORDER BY|\bLIMIT|\bJOIN|\)$|$)/is', $query, $join_matches);

        $join_conditions = [];
        if (!empty($join_matches[2])) {
            foreach ($join_matches[2] as $join_clause) {
                preg_match_all('/(\w+\.\w+|\w+)\s*(?:=|LIKE|IN|>|<|>=|<=|<>|!=)/i', $join_clause, $condition_matches);
                $join_conditions = array_merge($join_conditions, $condition_matches[1]);
            }
        }

        // Extract GROUP BY fields
        preg_match_all('/\bGROUP BY\s+(.*?)(?:\bHAVING|\bORDER BY|\bLIMIT|\)$|$)/is', $query, $group_matches);

        $group_fields = [];
        if (!empty($group_matches[1])) {
            $group_clause = $group_matches[1][0];
            preg_match_all('/(\w+\.\w+|\w+)/', $group_clause, $field_matches);
            $group_fields = $field_matches[1];
        }

        // Combine all fields that might benefit from indexes
        $index_candidates = array_merge($where_conditions, $join_conditions, $group_fields);

        // Format index suggestions
        $suggestions = [];
        foreach ($tables as $table) {
            $table_fields = [];
            foreach ($index_candidates as $field) {
                if (strpos($field, '.') !== false) {
                    list($table_name, $column) = explode('.', $field);
                    if ($table_name === $table) {
                        $table_fields[] = $column;
                    }
                } else {
                    // If no table prefix, it could be from any table
                    $table_fields[] = $field;
                }
            }

            if (!empty($table_fields)) {
                $table_fields = array_unique($table_fields);
                $suggestions[] = "CREATE INDEX idx_{$table}_" . implode('_', $table_fields) . " ON {$table}(" . implode(', ', $table_fields) . ");";
            }
        }

        if (!empty($suggestions)) {
            $suggestion_message = "SUGGESTED INDEXES for query (execution time: {$execution_time} sec):\n" . implode("\n", $suggestions);
            $CI->load->helper('file');
            write_file($log_file, date('Y-m-d H:i:s') . ' - ' . $suggestion_message . PHP_EOL, 'a+');
        }
    }
}
