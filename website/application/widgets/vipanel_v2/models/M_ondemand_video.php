<?php

class M_ondemand_video extends CI_Model
{
    function getOnDemandVideos($project_id){
        $query = $this->db->select('*')->from('ondemandvideo')->where(['project_id' => $project_id])->get();
        if ($query) {
            return $query->result();
        }
    }

    function getOnDemandVideoReport($project_id){
        $query1 = $this->db->select('ondemandvideo.id,ondemandvideo.title,ondemandvideo.name,TIME_FORMAT(SEC_TO_TIME(current_times.c_time),"%k:%i:%s") as c_time,TIME_FORMAT(SEC_TO_TIME(current_times.duration),"%k:%i:%s") as duration,current_times.finished, current_times.created_at, log.fullname,log.first_name,log.last_name,log.company,log.country,log.speciality,current_times.email,lang.iso as languagename')->from('ondemandvideo')->where(['ondemandvideo.project_id' => $project_id])
            ->join('current_times', 'ondemandvideo.id = current_times.video_id', 'inner')
            ->join('log', 'current_times.user_id = log.id', 'inner')
            ->join('lang', 'lang.id = ondemandvideo.lang_id', 'left')
            ->where('current_times.type_id', 1)
            ->get();
        $query2 = $this->db->select('ondemandvideo.id,ondemandvideo.title,ondemandvideo.name,TIME_FORMAT(SEC_TO_TIME(current_times.c_time),"%k:%i:%s") as c_time,TIME_FORMAT(SEC_TO_TIME(current_times.duration),"%k:%i:%s") as duration,current_times.finished, current_times.created_at, register.data, current_times.email,lang.iso as languagename')->from('ondemandvideo')->where(['ondemandvideo.project_id' => $project_id])
            ->join('current_times', 'ondemandvideo.id = current_times.video_id', 'inner')
            ->join('register', 'current_times.user_id = register.id', 'inner')
            ->join('lang', 'lang.id = ondemandvideo.lang_id', 'left')
            ->where('current_times.type_id', 0)
            ->get();

        $result = array();

        if ($query1) {
            $result1 = $query1->result();
            foreach ($result1 as $k1 => $r1) {
                if (!empty($r1->email)) {
                    $result[$k1]['id'] = $r1->id;
                    $result[$k1]['fullname'] = isset($r1->fullname) && !empty($r1->fullname) ? $r1->fullname : $r1->first_name . ' ' . $r1->last_name;
                    $result[$k1]['email'] = $r1->email;
                    $result[$k1]['country'] = $r1->country;
                    $result[$k1]['company'] = $r1->company;
                    $result[$k1]['languagename'] = $r1->languagename;
                    $result[$k1]['speciality'] = $r1->speciality;
                    $result[$k1]['c_time'] = $r1->c_time;
                    $result[$k1]['duration'] = $r1->duration;
                    $result[$k1]['title'] = $r1->title;
                    $result[$k1]['name'] = $r1->name;
                    $result[$k1]['finished'] = $r1->finished;
                    $result[$k1]['created_at'] = $r1->created_at;
                }
            }
        }
        if ($query2) {
            $result2 = $query2->result();
            $i = isset($k1) ? $k1 + 1 : 0;
            foreach ($result2 as $k2 => $r2) {
                $data = json_decode($r2->data);
                if (!empty($r2->email)) {
                    $result[$i]['id'] = $r2->id;
                    $result[$i]['fullname'] = isset($data->fullname) && !empty($data->fullname) ? $data->fullname : $data->first_name . ' ' . $data->last_name;
                    $result[$i]['email'] = $data->email;
                    $result[$i]['country'] = $data->country ?? '';
                    $result[$i]['speciality'] = $data->speciality ?? '';
                    $result[$i]['company'] = $data->company ?? '';
                    $result[$i]['languagename'] = $r2->languagename;
                    $result[$i]['c_time'] = $r2->c_time;
                    $result[$i]['duration'] = $r2->duration;
                    $result[$i]['title'] = $r2->title;
                    $result[$i]['name'] = $r2->name;
                    $result[$i]['finished'] = $r2->finished;
                    $result[$i]['created_at'] = $r2->created_at;

                    $i++;
                }
            }
        }

        return $result;
    }

    function createOnDemandVideo($project_id, $category_id, $lang_id, $all_countries, $country, $speciality, $title, $name, $speaker_affiliation, $speaker_bio, $description, $order, $image_url, $stream_date, $expired_date, $web, $ios, $android){
        $data = [
            'project_id' => $project_id,
            'category_id' => $category_id,
            'lang_id' => $lang_id,
            'all_countries' => $all_countries,
            'country' => $country,
            'speciality' => $speciality,
            'slug' => $this->generateRandomString(9),
            'title' => $title,
            'name' => $name,
            'speaker_affiliation' => $speaker_affiliation,
            'speaker_bio' => $speaker_bio,
            'description' => $description,
            'order' => $order,
            'img_url' => $image_url,
            'stream_date' => $stream_date,
            'expired_date' => $expired_date,
            'web' => $web,
            'ios' => $ios,
            'android' => $android
        ];
        $this->db->insert('ondemandvideo', $data);
        return $this->db->affected_rows();
    }

    function editOnDemandVideo($id, $category_id, $lang_id, $all_countries, $country, $speciality, $title, $name, $speaker_affiliation, $speaker_bio, $description, $order, $image_url, $stream_date, $expired_date, $web, $ios, $android){
        $data = [
            'category_id' => $category_id,
            'lang_id' => $lang_id,
            'all_countries' => $all_countries,
            'country' => $country,
            'speciality' => $speciality,
            'title' => $title,
            'name' => $name,
            'speaker_affiliation' => $speaker_affiliation,
            'speaker_bio' => $speaker_bio,
            'description' => $description,
            'order' => $order,
            'img_url' => $image_url,
            'stream_date' => $stream_date,
            'expired_date' => $expired_date,
            'web' => $web,
            'ios' => $ios,
            'android' => $android
        ];
        $this->db->where('id', $id)->update('ondemandvideo', $data);
        return $this->db->affected_rows();
    }

    function deleteOnDemandVideo($id){
        $this->db->delete('ondemandvideo', array('id' => $id));
        return $this->db->affected_rows();
    }

    function generateRandomString($length = 10){
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}