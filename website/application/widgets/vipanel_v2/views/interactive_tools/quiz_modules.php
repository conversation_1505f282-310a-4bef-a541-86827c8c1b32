<?php
$embeds = get_stream_embeds(site_config('project_id'));
$embeds = json_decode(json_encode($embeds ?? []), true);
$embeds = $this->security->xss_clean($embeds);
$embeds = json_decode(json_encode($embeds), false);

$this->load->helper('interactive_tools');
$quiz = new QuizQuestions();
$questions = $quiz->getQuestions();

$questions = json_decode(json_encode($questions ?? []), true);
$questions = $this->security->xss_clean($questions);
$questions = json_decode(json_encode($questions), false);
?>
<div class="table-responsive">
    <table id="quiz_list_table" class="table display text-nowrap">
        <thead>
        <tr>
            <th>Quiz No</th>
            <th>Question</th>
            <th>Type</th>
            <th>Time Limit</th>
            <th>Point</th>
            <th>Order</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($questions as $question): ?>
            <tr data-id="<?= $question->id ?>">
                <td><?= $question->quiz_no ?></td>
                <td><?= strLimit($question->question) ?></td>
                <td><?= $question->type ?></td>
                <td><?= $question->time_limit ?>s</td>
                <td><?= $question->question_point ?></td>
                <td><?= $question->order ?></td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="showEditQuizQuestion('<?= $question->id ?>')">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteQuizQuestion(<?= $question->id ?>)">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<script>
    const quizQuestions = <?= json_encode($questions) ?>;
    datatableOption.order = [
        [0, 'asc'],
        [5, 'asc'],
    ];

    datatableOption.buttons = [
        {
            text: 'Create Question',
            action: function () {
                $('#modal-add-new-quiz-question').modal('show')
            }
        },
        {
            text: 'Quiz Reports',
            action: function () {
                $('#modal-quiz-reports').modal('show');
            }
        },

    ];

    datatableOption.initComplete = function () {
        $(this.api().table().container()).find('input[type="search"]').parent().wrap('<form>').parent().attr('autocomplete', 'off').css('overflow', 'hidden').css('margin', 'auto');
        $("#quiz_list_table").wrap("<div style='overflow:auto; width:100%;position:relative;'></div>");

        $('.dt-buttons').addClass('btn-group');
        $('.dt-buttons .dt-button').addClass('btn btn-sm btn-warning');
        $('.dt-buttons .dt-button:not(:last-child)').css('margin-right', '10px');
    }
    const quiz_questions_table = $('#quiz_list_table').DataTable(datatableOption);

    function deleteQuizQuestion(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $('.preloader').fadeIn();
                $.ajax({
                    url: '/vipanel/v2/interactive-tools/save',
                    type: 'POST',
                    data: {
                        id: id,
                        type: 'delete_quiz_question',
                        '<?= $this->security->get_csrf_token_name(); ?>': '<?= $this->security->get_csrf_hash(); ?>'
                    },
                    success: function (result) {
                        $('.preloader').fadeOut();
                        if (result.status) {
                            Swal.fire(
                                'Deleted!',
                                'Quiz question has been deleted.',
                                'success'
                            );
                            const row = $('#quiz_list_table tbody tr[data-id=' + id + ']');
                            quiz_questions_table.row(row).remove().draw(false);
                        } else {
                            Swal.fire(
                                '',
                                'Something went wrong!',
                                'error'
                            )
                        }
                    },
                    error: function () {
                        Swal.fire(
                            '',
                            'Something went wrong!',
                            'error'
                        )
                        $('.preloader').fadeOut();
                    }
                })
            }
        })
    }

</script>



<?php
$this->load->helper('admin_settings');
$quiz_report_data = getQuizReport();
$quiz_columns = [
    'id' => 'Q ID',
    'project_id' => 'Project ID',
    'fullname' => 'Fullname',
    'quiz_no' => 'Quiz No',
    'embeds' => 'Embeds',
    'question' => 'Question',
    'order' => 'Order',
    'type' => 'Type',
    'option' => 'Answer',
    'created_at' => 'Answered Date',
    'result' => 'Result'
];
?>

<div class="modal fade" id="modal-quiz-reports" tabindex="-1" role="dialog" aria-labelledby="quizReportsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quizReportsModalLabel">Quiz Reports</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="quiz_reports_table" class="table border table-striped table-bordered display text-nowrap">
                                <thead>
                                    <tr>
                                        <?php foreach ($quiz_columns as $column) : ?>
                                            <th><?= $column ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($quiz_report_data)): ?>
                                        <?php foreach ($quiz_report_data as $row) : ?>
                                            <tr>
                                                <?php foreach ($quiz_columns as $key => $columnKey) : ?>
                                                    <?php if (is_object($row) && !empty($row->{$key})) : ?>
                                                        <?php $value = $row->{$key} ?? "-" ?>
                                                        <?php if (isStringDate($value)) : ?>
                                                            <td data-order="<?= strtotime($value) ?>" class="date-row">
                                                                <?php $date = edit_timezone($value, $site_timezone); ?>
                                                                <?= $date->format($site_date_format); ?>
                                                            </td>
                                                        <?php else : ?>
                                                            <td><?= !empty(trim($value)) ? $value : "-" ?></td>
                                                        <?php endif; ?>
                                                    <?php elseif (is_array($row) && !empty($row[$key])) : ?>
                                                        <?php $value = $row[$key] ?? "-" ?>
                                                        <?php if (isStringDate($value)) : ?>
                                                            <td data-order="<?= strtotime($value) ?>" class="date-row">
                                                                <?php $date = edit_timezone($value, $site_timezone); ?>
                                                                <?= $date->format($site_date_format); ?>
                                                            </td>
                                                        <?php else : ?>
                                                            <td><?= !empty(trim($value)) ? $value : "-" ?></td>
                                                        <?php endif; ?>
                                                    <?php else : ?>
                                                        <td>-</td>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>

    $(document).ready(function() {
        const quizReportTitle = host + '_quiz_report_' + fileDateSuffix;

        const quizReportOptions = Object.assign({}, datatableOption);
        quizReportOptions.autoWidth = false;
        quizReportOptions.scrollX = true;
        quizReportOptions.buttons = [
            {
                extend: 'copyHtml5',
                className: 'btn btn-warning btn-sm',
                title: quizReportTitle
            }, {
                extend: 'csvHtml5',
                className: 'btn btn-warning btn-sm',
                title: quizReportTitle
            }, {
                extend: 'excelHtml5',
                className: 'btn btn-warning btn-sm',
                title: quizReportTitle
            }, {
                extend: 'pdfHtml5',
                className: 'btn btn-warning btn-sm',
                title: quizReportTitle
            }, {
                extend: 'print',
                className: 'btn btn-warning btn-sm',
                title: quizReportTitle
            }
        ];
        quizReportOptions.initComplete = function () {
            $(this.api().table().container())
                .find('input[type="search"]')
                .parent()
                .wrap('<form>')
                .parent()
                .attr('autocomplete', 'off')
                .css('overflow', 'hidden')
                .css('margin', 'auto');

            $('.dt-buttons').addClass('btn-group');
            $('.dt-buttons .dt-button').addClass('btn btn-sm btn-warning');
        };

        $('#modal-quiz-reports').on('shown.bs.modal', function () {
            if (!$.fn.DataTable.isDataTable('#quiz_reports_table')) {
                $('#quiz_reports_table').DataTable(quizReportOptions);
            }
        });
    });
</script>

<?php
$this->load->view('interactive_tools/partials/quiz_question_create', [
    'embeds' => $embeds,
]);
$this->load->view('interactive_tools/partials/quiz_question_edit', [
    'embeds' => $embeds,
]);
?>