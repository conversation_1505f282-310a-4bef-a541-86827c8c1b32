<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * VipanelFrameHook Class
 *
 * This hook overrides the X-Frame-Options header for vipanel routes
 * to allow embedding in iframes.
 */
class VipanelFrameHook {

    /**
     * Override X-Frame-Options header for vipanel routes
     */
    public function process()
    {
        $CI =& get_instance();
        
        // Check if the current route is a vipanel route
        $uri = $CI->uri->uri_string();
        if (strpos($uri, 'vipanel') === 0) {
            // Remove any existing X-Frame-Options header
            header_remove('X-Frame-Options');
            
            // Set a more permissive Content-Security-Policy instead
            header("Content-Security-Policy: frame-ancestors 'self' *;");
            header("X-Frame-Options: DENY");
            header("X-Content-Type-Options: nosniff");
            header("Content-Security-Policy: default-src * 'self' 'unsafe-inline' 'unsafe-eval'; script-src * 'self' 'unsafe-inline' 'unsafe-eval'; object-src *; img-src * 'self' data: blob:;");
            header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
            header("Referrer-Policy: strict-origin-when-cross-origin");
        }
    }
}
