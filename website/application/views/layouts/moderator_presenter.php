<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title><?php echo isset($this->session->userdata('site_config')->name) ? $this->session->userdata('site_config')->name : '' ?></title>

    <?php $this->load->view("inc/header_common_files"); ?>

    <script>
      var current_site_language = "<?php echo $this->input->cookie('site_lang', true); ?>";
    </script>

    <?php
    $siteUrl = isset($this->session->userdata('site_config')->url) ? $this->session->userdata('site_config')->url : '';
    ?>

    <style type="text/css">
        html, body, * {
            font-family: arial, sans-serif;
        }

        .questionsArea {
            scroll-behavior: auto;
        }

        .img-responsive{
            max-height: 100px;
        }
    </style>

</head>
<body>

<div class="stickyWrapper">
    <!-- HEADER -->
    <div class="row header customBg2">
        <div class="container">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
                <div class="logoTop">
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
                <div class="topFlags">
                    <ul>
                        <li class="activeFlag">
                            <a href="javascript:void(0);" onclick="change_language(1);" class="flag_TR"></a>
                        </li>
                        <li class="activeFlag">
                            <a href="javascript:void(0);" onclick="change_language(2);" class="flag_EN"></a>
                        </li>
                    </ul>
                </div>
                <?php if ($this->session->has_userdata('site_moderator') || $this->session->has_userdata('site_presenter')) { ?>
                    <?php
                    if ($this->session->userdata('site_presenter') !== null) {
                        $logoutUrl = "/presenter/logout";
                    }
                    if ($this->session->userdata('site_moderator') !== null) {
                        $logoutUrl = "/moderator/logout";
                    }
                    ?>
                    <div class="userName">
                        <a href="<?php echo $logoutUrl; ?>" class="nameText customText1"><?php echo $this->lang->line('logout'); ?>&nbsp;<i class="fa fa-sign-out"></i></a>
                    </div>

                <?php } ?>

                <div class="clear"></div>
            </div>
        </div>
    </div>
    <!-- /HEADER -->

    <!-- CONTENT -->
    <?php echo $content_for_layout; ?>
    <!-- /CONTENT -->

</div>

<?php $this->load->view('inc/footer_common_files'); ?>

<?php

if ($siteUrl) {
    $cssFile = ASSETPATH . 'static/' . $siteUrl . '/custom.css';
    $jsFile = ASSETPATH . 'static/' . $siteUrl . '/custom.js';
    if (file_exists($cssFile)) {
        echo '<link rel="stylesheet" href="/assets/static/' . $siteUrl . '/custom.css" />';
    }

    if (file_exists($jsFile)) {
        echo '<script type="text/javascript" src="/assets/static/' . $siteUrl . '/custom.js"></script>';
    }
}

?>

</body>
</html>
