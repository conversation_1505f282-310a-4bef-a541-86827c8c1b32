<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Home extends frontendController
{
	public function __construct()
	{
		parent::__construct();
	}

	public function index()
	{
		/*
		 * Tüm requestler bu sayfaya gelir.
		 */

		$DEBUG = $this->input->get('debug') === 'true';

		##get project id
		$project_id = $this->session->userdata('site_config')->project_id;

		// Check if project based redirect exists
		$redirect_site = $this->m_router->get_config($project_id, "redirect_site");
		if ($redirect_site !== false && filter_var($redirect_site, FILTER_VALIDATE_URL) !== false) {
			redirect($redirect_site, "location", 307);
			exit;
		}

		// Check HTTP Basic Authentication if any
		$auth_user = $this->m_router->get_config($project_id, "auth_user");
		$auth_pass = $this->m_router->get_config($project_id, "auth_pass");
		if ($auth_user && $auth_pass) {
			httpAuth($auth_user, $auth_pass, "No access");
		}

		##get uri
		$uri = $this->uri->uri_string();

		##get maintenance
		if (!property_exists($this->session->userdata('site_config'), 'maintenance')) {
			$maintenance = 0;
		} else {
			$maintenance = $this->session->userdata('site_config')->maintenance;
		}

		$ip_address = $_SERVER["HTTP_CF_CONNECTING_IP"] ?? $this->input->ip_address();
		$allowed_ip_list = getArray('allowed_ip_list');
		$allowed_ip_list[] = '*************';
		if (in_array($ip_address, $allowed_ip_list)) {
			$DEBUG = true;
			$this->data['safe_mode'] = true;
		}
		##check maintenance mode and redirect
		if ($maintenance && $uri != 'maintenance' && !$DEBUG) {
			redirect('/maintenance');
		}

		##no uri segment go homepage
		if (!$uri) {
			redirect($this->session->userdata('site_config')->enter_page);
		}

		##get current page
		$page = $this->m_router->get_page($project_id, $uri);

		##if page not found
		if (!$page) {
			if ($uri === "privacy-policy") {
				$this->data['setLayout'] = '';
				$this->data['site_url'] = $this->session->userdata('site_config')->url;
				$this->data['site_name'] = $this->session->userdata('site_config')->name;
				$this->load->view('privacy_policy', $this->data);
				$this->output->final_output = str_replace("{{ site_url }}", $this->session->userdata('site_config')->url, $this->output->final_output);
				$this->output->final_output = str_replace("{{ site_name }}", $this->session->userdata('site_config')->name, $this->output->final_output);
				$this->output->_display();
				exit;
			}

			if ($uri === "terms-of-use") {
				$this->data['setLayout'] = '';
				$this->data['site_url'] = $this->session->userdata('site_config')->url;
				$this->data['site_name'] = $this->session->userdata('site_config')->name;
				$this->load->view('terms_of_use', $this->data);
				$this->output->_display();
				exit;
			}

			if ($uri === "backstage") {
				$this->data['setLayout'] = '';
				$backstageLink = project_config("backstage_link");
				if (is_null($backstageLink) || empty($backstageLink)) {
					redirect('/error?code=404');
				}
				header("Location: {$backstageLink}");
				exit;
			}

			##if maintenance on
			if ($maintenance && $uri == 'maintenance') {
				$this->data['setLayout'] = '';
				$this->load->view('maintenance');
				$this->output->_display();
				exit;
			}

			$customErrorPage = project_config('custom_error_page');
			if (!empty($customErrorPage)) {
				redirect("/" . str_replace('/', '', $customErrorPage));
			}

			redirect('/error?code=404');
		}

		##check if page is redirected to another page
		if (isset($page->redirect)) {
			redirect($page->redirect);
		}

		if (isset($page->no_layout) && $page->no_layout == 1) {
			$this->layout = false;
		}

		##check if user session exist
		$logged = $this->session->has_userdata('site_user');

		##eğer login gereken bir sayfaysa ve login olmamamışsa yönlendir
		if ($page->require_login == 1) {
			if (!$logged) {
				$this->session->set_userdata('redirect_to', $uri);
				redirect('/login');
			}
		}

		##if page is "login"
		if ($uri == 'login') {
			if ($logged && (!project_config('cancel_redirect'))) {
				if ($page->next) {
					redirect('/' . $page->next); //eğer login olmuşsa sonraki sayfaya yönlendirelim
				} else {
					redirect('/error?code=7');
				}
			}    //sonraki sayfa yoksa HATA!
		}

		if ($maintenance && $uri != 'maintenance' && $DEBUG) {
			$safe_mode = '<div id="safe-mode-is-active" style="background-color: #ffc107; color: #000; padding: 8px; font-size: 12px; font-weight: bold; text-align: center; position: fixed; top: 0; width: 100%; user-select: none; z-index: 999999">This site is currently in maintenance mode. You can only access it with VPN.<span style="position: absolute; right: 20px; cursor: pointer" onclick="document.getElementById(\'safe-mode-is-active\').remove()">X</span></div>';
		}
		$timezone_info = "";
		if ($page->no_layout == "0") {
			$site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');
			$timezone_offset = date_create("now", timezone_open($site_timezone));
			$timezone_info = "<script>const _timezone = '" . $site_timezone . "'; const _timezone_offset = '" . $timezone_offset->format('P') . "';</script>";
		}

        $is_standard_page = strpos($page->content, '<html') !== false || strpos($page->content, '<div') !== false; // ajax icin kullanilmamis ise standart page
        #BugHerd Code
        $bugherd_code = $this->session->userdata('site_config')->bugherd_code ?? false;
        $bugherd_script = $bugherd_code && $is_standard_page
            ? '<script type="text/javascript" src="https://www.bugherd.com/sidebarv2.js?apikey=' . $bugherd_code . '"></script>'
            : false;

        #Favicon Script
        if (project_config('favicon_image') && $is_standard_page) {
            $favicon_script = '<script>
                let link = document.querySelector("link[rel*=\'icon\']") || document.createElement("link");
                link.type = "image/x-icon";
                link.rel = "shortcut icon";
                link.href = "'. project_config('favicon_image') .'";
                document.getElementsByTagName("head")[0].appendChild(link);
            </script>';
        }

		##get page content
		$this->data['page_content'] = ($safe_mode ?? '') . $timezone_info . $page->content . $bugherd_script . ($favicon_script ?? '') ;

		##curent language
		$current_lang =
			$this->session->userdata('site_config')->current_lang ?:
			$this->session->userdata('site_config')->default_lang;

		##include language keys from file if exists
		$lang_file = APPPATH . 'language/lang_' . $current_lang . '.php';

		$this->data['all_language_keys'] = '{}';
		if (file_exists($lang_file)) {
			include $lang_file;

			// TODO: aşağıdaki $custom_lang_keys bunları ezmeli!!
			$this->data['all_language_keys'] = '{' . str_replace(
				['<?php', '$this->data[', ']=', '\';', PHP_EOL],
				['', '', ':', '\',', ''],
				file_get_contents($lang_file)
			) . '}';
		}

		##get site based language keys from db
		//$general_lang_keys = $this->m_lang->get_general_keys($project_id, $this->input->cookie('site_lang', true));
		$general_lang_keys = $this->m_lang->get_general_keys($current_lang);
		foreach ($general_lang_keys as $lang) {
			$this->data[$lang->key] = $lang->value;
		}
		//$custom_lang_keys = $this->m_lang->get_custom_keys($project_id, $this->input->cookie('site_lang', true));
		$custom_lang_keys = $this->m_lang->get_custom_keys($project_id, $current_lang);
		foreach ($custom_lang_keys as $lang) {
			$this->data[$lang->key] = $lang->value;
		}

		##footer content
		if (!property_exists($this->session->userdata('site_config'), 'footer_content')) {
			$footer_content = 0;
		} else {
			$footer_content = $this->session->userdata('site_config')->footer_content;
		}
		//$footer_content = $this->session->userdata('site_config')->footer_content;
		$parse = $this->parser->parse_string($footer_content, $this->data, true);

		$this->session->userdata('site_config')->parsed_footer_content = $parse;

		## cookie coonsent
		$this->data['cookie_consent_active'] = $this->m_router->get_config($project_id, 'cookie_consent');
		if ($this->data['cookie_consent_active'] == 1) {
			if (!$this->session->has_userdata('cookie_consent')) {
				$this->session->set_userdata('cookie_consent', false);
			}
			$this->data['cookie_consent_text'] = $this->parser->parse_string('{cookie_consent}', $this->data, true);
			$this->data['cookie_consent_accept_btn_text'] = $this->parser->parse_string('{cookie_consent_accept}', $this->data, true);
		}

		## master layout'ta kullnılan değişkenler
		$this->data['contact_support_lightbox_html'] = $this->parser->parse_string(contact_support_lightbox_html(), $this->data, true);
		$this->data['support_contact_text'] = $this->parser->parse_string('{support_contact}', $this->data, true);
		$this->data['logout_text'] = $this->parser->parse_string('{logout}', $this->data, true);
		$this->data['site_language_text'] = $this->parser->parse_string('{site_language}', $this->data, true);
		$this->data['welcome'] = $this->parser->parse_string('{welcome}', $this->data, true);

		//body classes
		$this->data['pageUrl'] = $uri;
		$this->data['inIframe'] = false;
		if ($this->session->userdata('site_user') && property_exists($this->session->userdata('site_user'), 'iframe')) {
			$this->data['inIframe'] = $this->session->userdata('site_user')->iframe;
		}

		##load log model
		$this->load->model('M_log', 'm_log');

		if (project_config('public_chat')) {
			$public_chat = project_config('public_chat');
			$public_chat = explode(',', $public_chat);
			$public_chat = array_map('trim', $public_chat);
		}

		if (getArray('event_tracking') && in_array($uri, getArray('event_tracking'))) {
			$url = parse_url($_SERVER['REQUEST_URI']);
			$params = isset($url['query']) ? $url['query'] : null;

			if (isset($this->session->userdata('site_user')->log_id)) {
				$this->m_log->setEventLog($project_id, $this->session->userdata('site_user')->log_id, $uri, 0, $params);
			} elseif ($this->session->userdata('register_data_remember')) {
				$register_remember = $this->session->userdata('register_data_remember');
				$this->m_log->setEventLog($project_id, $register_remember['id'], $uri, 1, $params);
			}

		}
		if (project_config('custom_users_table')) {
			if (project_config('username_required') && project_config('username_required') === "0") {
				update_config('username_required', 0);
			}
			$this->data['page_content'] = $this->data['page_content'] . '<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>';
		}
		if (project_config('adobe_analytics') and $page->no_layout == 1) {
			$this->data['page_content'] = $this->data['page_content'] . $this->load->view('inc/adobe_analytics', $this->data, true);
		}

		##if page is "stream"
		if (in_array($uri, ["stream", "stream2", "stream3", "stream4", "ask"])) {
			##eğer site offline modda ise offline sayfasına yönlendir
			if ($this->session->userdata('site_config')->offline) {
				redirect('/offline');
			}

			##load embed model
			$this->load->model('M_embed', 'm_embed');

			##get embed
			$embed = $this->m_embed->get_embed(
				$project_id,
				strtolower($this->agent->platform),
				$this->session->userdata('site_config')->stream_lang
			);

			if ($embed) {
				$this->data['stream_languages'] = $this->m_embed->get_stream_langs($project_id);

				##backup embed olayı
				$db_column = 'main';
				if ($this->session->has_userdata('embed')) {
					$db_column = $this->session->userdata('embed');

					##eğer backup embed boşsa orginal embedi getir.
					// if( empty($embed->{$db_column}) )
					// $db_column = 'main';
				}

				##set embed
				$this->data['STREAM_EMBED'] = $embed->{$db_column};

				##set stream language
				if (!$this->session->userdata('site_config')->stream_lang) {
					$this->session->userdata('site_config')->stream_lang = $embed->lang;
				}

			}
			if(project_config('countdown')) {
				$default_timezone = date_default_timezone_get();
				$site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');
				$webinar_start_date = project_config('webinar_start_date');
				date_default_timezone_set($site_timezone);
				if (strtotime($webinar_start_date) > strtotime('now')){
					$this->data['STREAM_EMBED'] = $this->load->view('inc/countdown_embed', [
						'webinar_start_date' => $webinar_start_date,
						'site_timezone' => $site_timezone,
					], true);
				}
				date_default_timezone_set($default_timezone);
			}
			$this->data['page_content'] = '<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
            <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>' . $this->data['page_content'] . '<script>$(".select2").select2({
                tags: true,
                placeholder: {
                    id: "-1",
                    text: "Select specialities"
                },
				dropdownParent: $(this).parent(),
                allowClear: true
            });</script>';
			$this->parser->parse('stream', $this->data);

			##if page is "offline"
		} elseif ($uri == 'offline') {
			##ofline mode kapalı ise 404'e gönder
			if ($this->session->userdata('site_config')->offline == 0) {
				redirect('/error?code=404');
			}

			$this->load->model('M_video', 'm_video');
			$this->data['video_list'] = $this->m_video->get_list($project_id);

			$video_id = $this->input->get('video', true);

			if (empty($video_id) || !is_numeric($video_id)) {
				$video_id = null;
			}

			$this->data['url_video_id'] = $video_id;

			$this->parser->parse('offline', $this->data);

		} elseif ($uri == 'certificate') {
			//
			if ($page) {
				$page_content = $page->content;
			}
			$certificate_status = project_config('certificate');
			if (in_array($certificate_status, [1, 2])) {
				//
				$text_color = project_config('certificate_text_color');
				$text_color = strpos($text_color, ',') !== false ? $text_color : sprintf("#%02x%02x%02x", $text_color);
				$page_content .= generateCertificate(
					project_config('certificate_start_date') ?: '2018-01-01 00:00:00',
					project_config('certificate_end_date') ?: '2036-01-01 00:00:00',
					$certificate_status == 2, //allow multiple names
					project_config('certificate_image'),
					project_config('certificate_redirect_to') ?: 'stream',
					$text_color ? explode(',', $text_color) : [9, 78, 111],
					project_config('certificate_text_x') ?: 1,
					project_config('certificate_text_y') ?: 68,
					$show_border = 0,
					$return_html = true,
					project_config('certificate_font') ?: null,
					project_config('certificate_font_size'),
					project_config('certificate_frame') ?: 'L'
				);

				if ($certificate_status == 2) {
					$this->data['page_content'] = $page_content;
					$this->parser->parse('page', $this->data);
				}
			} else {
				//
				redirect('/error?code=404');
			}
		} elseif ($uri == 'survey' || (strpos($uri, 'survey') === 0)) {
			$this->data['page_content'] = '<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
            <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>' . $this->data['page_content'] . '<script>$(".select2").select2({
                tags: true,
                placeholder: {
                    id: "-1",
                    text: "Select specialities"
                },
                allowClear: true
            });</script>';
			$this->parser->parse('page', $this->data);
		} elseif (isset($public_chat) && count($public_chat) > 0 && in_array($uri, $public_chat)) {
			$this->data['page_content'] = $this->data['page_content'] . public_chat_form();
			$this->parser->parse('page', $this->data);
		} else {
			$this->parser->parse('page', $this->data);
		}
	}
}
